import fetch from 'node-fetch';
import { promises as fs } from 'fs';

const query = `
query ($id: Int) {
  Media (id: $id, type: ANIME) {
    id
    title {
      romaji
      english
      native
    }
    synonyms
    popularity
  }
}
`;

const url = 'https://graphql.anilist.co';
const outputFile = 'anime_data.json';

const getRandomDelay = (min, max) => 
  (Math.random() * (max - min) + min) * 1000; // Returns milliseconds

const fetchAnimeData = async (id) => {
  const variables = { id };
  const options = {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    },
    body: JSON.stringify({ query, variables })
  };

  try {
    const response = await fetch(url, options);
    const data = await response.json();
    
    if (data.errors) {
      console.error(`Error fetching anime with ID ${id}:`, data.errors[0].message);
      return null;
    }
    
    return data.data.Media;
  } catch (error) {
    console.error(`Error fetching anime with ID ${id}:`, error);
    return null;
  }
};

const updateJsonFile = async (animeData) => {
  try {
    let existingData = [];
    try {
      const fileContent = await fs.readFile(outputFile, 'utf-8');
      existingData = JSON.parse(fileContent);
    } catch (error) {
      // File doesn't exist or is empty, start with an empty array
    }

    existingData.push(animeData);
    await fs.writeFile(outputFile, JSON.stringify(existingData, null, 2));
    console.log(`Updated ${outputFile} with anime ID ${animeData.id} (Popularity: ${animeData.popularity})`);
  } catch (error) {
    console.error('Error updating JSON file:', error);
  }
};

const fetchMultipleAnime = async (startId, endId) => {
  let fetchedCount = 0;
  let notFoundCount = 0;

  for (let id = startId; id <= endId; id++) {
    console.log(`Fetching anime with ID ${id}...`);
    const animeData = await fetchAnimeData(id);
    
    if (animeData) {
      await updateJsonFile(animeData);
      fetchedCount++;
    } else {
      console.log(`Anime with ID ${id} not found or error occurred.`);
      notFoundCount++;
    }
    
    const delay = getRandomDelay(1, 5);
    console.log(`Waiting for ${(delay / 1000).toFixed(2)} seconds before next request...`);
    await new Promise(resolve => setTimeout(resolve, delay));
  }

  return { fetchedCount, notFoundCount };
};

const main = async () => {
  try {
    const { fetchedCount, notFoundCount } = await fetchMultipleAnime(89000, 181078);
    console.log(`Fetch complete. Successfully fetched: ${fetchedCount}, Not found or errors: ${notFoundCount}`);
    console.log('All data has been saved to anime_data.json');
  } catch (error) {
    console.error('Error in main function:', error);
  }
};

main();