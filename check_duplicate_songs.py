#!/usr/bin/env python3
"""
Script to check for duplicate annSongId values in shuffle/songs.json
"""

import json
import sys
from collections import Counter
from pathlib import Path


def check_duplicate_ann_song_ids(file_path):
    """
    Check for duplicate annSongId values in the JSON file.
    
    Args:
        file_path (str): Path to the songs.json file
        
    Returns:
        tuple: (all_ids, duplicates, duplicate_count)
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except FileNotFoundError:
        print(f"Error: File '{file_path}' not found.")
        return None, None, 0
    except json.JSONDecodeError as e:
        print(f"Error: Invalid JSON in file '{file_path}': {e}")
        return None, None, 0
    
    # Extract all annSongId values
    all_ann_song_ids = []
    
    # Navigate through the JSON structure
    if 'ruleBlocks' in data:
        for rule_block in data['ruleBlocks']:
            if 'blocks' in rule_block:
                for block in rule_block['blocks']:
                    if 'annSongId' in block:
                        all_ann_song_ids.append(block['annSongId'])
    
    # Count occurrences of each annSongId
    id_counts = Counter(all_ann_song_ids)
    
    # Find duplicates (IDs that appear more than once)
    duplicates = {ann_id: count for ann_id, count in id_counts.items() if count > 1}
    
    return all_ann_song_ids, duplicates, len(duplicates)


def main():
    # Default file path
    file_path = "shuffle/songs.json"
    
    # Allow custom file path as command line argument
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
    
    print(f"Checking for duplicate annSongId values in: {file_path}")
    print("=" * 60)
    
    all_ids, duplicates, duplicate_count = check_duplicate_ann_song_ids(file_path)
    
    if all_ids is None:
        sys.exit(1)
    
    total_songs = len(all_ids)
    unique_songs = len(set(all_ids))
    
    print(f"Total songs found: {total_songs}")
    print(f"Unique songs: {unique_songs}")
    print(f"Duplicate annSongId entries: {duplicate_count}")
    print()
    
    if duplicates:
        print("DUPLICATES FOUND:")
        print("-" * 40)
        for ann_id, count in sorted(duplicates.items()):
            print(f"annSongId {ann_id}: appears {count} times")
        
        print()
        print("Summary:")
        total_duplicate_entries = sum(duplicates.values()) - len(duplicates)
        print(f"- {duplicate_count} unique annSongId values have duplicates")
        print(f"- {total_duplicate_entries} extra entries could be removed")
        
        # Return exit code 1 to indicate duplicates were found
        sys.exit(1)
    else:
        print("✅ No duplicate annSongId values found!")
        print("All songs have unique annSongId values.")
        sys.exit(0)


if __name__ == "__main__":
    main()
