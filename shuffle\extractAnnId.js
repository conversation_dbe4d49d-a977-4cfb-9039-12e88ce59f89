import { readFile, writeFile } from 'fs/promises';

const extractAnnSongIds = async () => {
  try {
    // Read the input file (assuming it's named wishlist.json)
    const data = await readFile('shuffle/songs.json', 'utf8');

    // Parse the JSON data
    const wishlistData = JSON.parse(data);

    // Extract all annSongId values from the blocks array
    const annSongIds = [];

    wishlistData.ruleBlocks.forEach(ruleBlock => {
      if (ruleBlock.blocks && Array.isArray(ruleBlock.blocks)) {
        ruleBlock.blocks.forEach(block => {
          if (block.annSongId) {
            annSongIds.push(block.annSongId);
          }
        });
      }
    });

    // Write the extracted IDs to a new file
    await writeFile('ann_song_ids.json', JSON.stringify(annSongIds, null, 2));

    console.log(`Extraction complete. Found ${annSongIds.length} annSongId values.`);
  } catch (error) {
    console.error('Error:', error.message);
  }
};

extractAnnSongIds();