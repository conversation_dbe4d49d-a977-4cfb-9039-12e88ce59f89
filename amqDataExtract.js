import { readFile, writeFile } from 'fs/promises';

// Array of annIds to filter by
const targetAnnIds = [
  37923,
  39290,
  19537,
  30361,
  13047,
  1657,
  42633,
  18713,
  44082,
  14854,
  70,
  5067,
  11869,
  39450,
  37109,
  27892,
  334,
  39814,
  22066,
  1292,
  911,
  1255,
  12993,
  36361,
  42228,
  32931,
  1157,
  24022,
  42527,
  5470,
  33477,
  39081,
  16357,
  16599,
  12961,
  478,
  34869,
  18491,
  15257,
  1446,
  7565,
  32386,
  40965,
  35769,
  28454,
  44036,
  3421,
  36663,
  36154,
  42822,
  11391,
  14017,
  6418,
  11053,
  3401,
  43349,
  23299,
  18904,
  42266,
  32561,
  15557,
  10430,
  7265,
  1608,
  33132,
  36226,
  39678,
  8280,
  22699,
  43316,
  11693,
  29735,
  35473,
  28463,
  15820,
  5638,
  42003,
  30325,
  20651,
  32411,
  326,
  7580,
  44122,
  1075,
  36732,
  34863,
  11655,
  31222,
  4162,
  38324,
  27658,
  34827,
  42290,
  42660,
  8291,
  5209,
  18689,
  24633,
  1081,
  9348,
  37051,
  32619,
  11627,
  27790,
  41112,
  42832,
  2334,
  9308,
  21506,
  7605,
  22308,
  36993,
  16313,
  36409,
  28764,
  18788,
  38498,
  31188,
  10245,
  18165,
  12050,
  10538,
  48,
  6357,
  16314,
  15303,
  38715,
  14185,
  9596,
  6221,
  9123,
  42648,
  23696,
  13496,
  38175,
  21890,
  21705,
  13786,
  38245,
  12392,
  424,
  13001,
  16612,
  10707,
  40652,
  23036,
  11602,
  28243,
  42678,
  36911,
  11110,
  9345,
  12297,
  15446,
  23219,
  12990,
  10390,
  20573,
  33931,
  22386,
  26584,
  41343,
  2340,
  6465,
  21880,
  13290,
  37019,
  2104,
  12209,
  17939,
  10524,
  22257,
  43335,
  7602,
  12083,
  43486,
  42658,
  44411,
  24902,
  574,
  38093,
  6470,
  38014,
  7616,
  15192,
  15918,
  28398,
  43151,
  11073,
  43939,
  14146,
  31857,
  32483,
  7730,
  5380,
  17954,
  16643,
  37922,
  8523,
  14701,
  648,
  13742,
  15340,
  53,
  15167,
  14933,
  935,
  23039,
  43633,
  2305,
  41341,
  38495,
  35605,
  2100,
  16263,
  22233,
  39252,
  17864,
  22042,
  42840,
  11278,
  16131,
  969,
  670,
  40279,
  39482,
  24157,
  9166,
  7990,
  14456,
  1012,
  2223,
  42545,
  22825,
  1068,
  16307,
  39375,
  49,
  5311,
  25950,
  10387,
  20027,
  32929,
  532,
  2219,
  10908,
  22898,
  925,
  37297,
  13605,
  44091,
  25989,
  37089,
  16627,
  29029,
  42664,
  14815,
  16735,
  22243,
  9280,
  10656,
  14927,
  34179,
  25122,
  6363,
  4157,
  10930,
  6824,
  38601,
  16434,
  6834,
  4156,
  12417,
  29848,
  4155,
  39823,
  22488,
  5365,
  14190,
  30257,
  8016,
  13688,
  33381,
  12995,
  13379,
  1909,
  12596,
  20902,
  747,
  8216,
  1705,
  42659,
  37758,
  43673,
  3076,
  10680,
  14157,
  14241,
  12963,
  10894,
  9150,
  33768,
  31164,
  9535,
  1177,
  10378,
  37481,
  42528,
  16270,
  6003,
  6423,
  40624,
  12588,
  33392,
  44020,
  14432,
  36433,
  38023,
  41340,
  30022,
  31265,
  30021,
  16776,
  6114,
  38494,
  34205,
  10539,
  34278,
  39321,
  43403,
  30745,
  27331,
  33386,
  13897,
  27720,
  8595,
  18403,
  7394,
  26442,
  921,
  6210,
  41102,
  8649,
  31498,
  22442,
  2224,
  1750,
  11260,
  39318,
  10711,
  34255,
  39317,
  13439,
  10471,
  38051,
  5137,
  13250,
  7611,
  3406,
  17691,
  7268,
  15559,
  4814,
  15447,
  34495,
  16123,
  10283,
  5554,
  13362,
  15312,
  1955,
  12992,
  3217,
  42727,
  36283,
  19770,
  39291,
  22017,
  38700,
  15633,
  8796,
  436,
  6808,
  19544,
  6807,
  31247,
  38017,
  38412,
  11245,
  6407,
  6830,
  13524,
  13215,
  24584,
  15563,
  1606,
  9005,
  14661,
  24694,
  18016,
  4929,
  28682,
  19345,
  15997,
  30253,
  31159,
  26999,
  8278,
  17574,
  7937,
  16668,
  9976,
  7619,
  14258,
  40582,
  42194,
  2374,
  42008,
  14327,
  21275,
  45,
  24316,
  42714,
  8882,
  35909,
  38522,
  11334,
  29446,
  31823,
  8534,
  9915,
  3215,
  20338,
  27731,
  5368,
  3275,
  15522,
  3080,
  12170,
  34632,
  405,
  38547,
  22678,
  11745,
  18942,
  8860,
  11682,
  9758,
  14148,
  13110,
  20533,
  13693,
  11762,
  13234,
  13015,
  9232,
  43305,
  28958,
  1954,
  10841,
  38635,
  43938,
  13262,
  1029,
  12896,
  44119,
  42649,
  8861,
  15314,
  36643,
  18757,
  35972,
  18007,
  23164,
  26256,
  4779,
  14014,
  11085,
  12982,
  15290,
  41142,
  10652,
  10986,
  22186,
  10569,
  19528,
  23654,
  13427,
  10097,
  43494,
  1288,
  10730,
  10904,
  16382,
  1632,
  41305,
  3733,
  42030,
  30227,
  42609,
  24531,
  19328,
  922,
  25986,
  9659,
  35621,
  33482,
  15725,
  13722,
  37068,
  13810,
  16340,
  18437,
  19572,
  7020,
  16358,
  16390,
  33652,
  25190,
  32883,
  4276,
  33142,
  34286,
  452,
  11753,
  14136,
  24581,
  44035,
  6431,
  35757,
  10372,
  1498,
  43414,
  6359,
  35251,
  15194,
  5832,
  19891,
  42229,
  14131,
  43323,
  18773,
  37963,
  43966,
  36330,
  8459,
  43974,
  44426,
  15650,
  1735,
  14698,
  36177,
  6448,
  676,
  42221,
  11747,
  11759,
  20973,
  32447,
  39374,
  39824,
  13839,
  36034,
  22043,
  14128,
  7941,
  35136,
  26419,
  14520,
  33246,
  39877,
  42606,
  543,
  15344,
  9229,
  35811,
  41700,
  7671,
  15636,
  11837,
  10810,
  16374,
  7548,
  6455,
  13804,
  1729,
  4442,
  40652,
  23971,
  29152,
  9831,
  4381,
  34555,
  25938,
  30869,
  13038,
  24,
  15712,
  12764,
  31868,
  43327,
  12219,
  39372,
  1017,
  11320,
  29798,
  9717,
  37022,
  1996,
  13023,
  28011,
  6533,
  1674,
  16473,
  17611,
  26884,
  20406,
  9045,
  14923,
  4167,
  6819,
  15914,
  18344,
  26571,
  1080,
  13128,
  1721,
  26973,
  22069,
  25794,
  13774,
  26391,
  58,
  3479,
  158,
  197,
  6134,
  32502,
  26526,
  31614,
  8992,
  18926,
  23987,
  18678,
  17952,
  19628,
  13329,
  52,
  33662,
  2072,
  11411,
  1183,
  23150,
  6230,
  43995,
  31183,
  33480,
  14138,
  28396,
  1114,
  39598,
  43511,
  1181,
  43935,
  44090,
  27938,
  9088,
  35731,
  25883,
  37889,
  26289,
  15149,
  39679,
  33056,
  3216,
  27350,
  22052,
  20761,
  311,
  11776,
  26206,
  9594,
  12349,
  25748,
  5186,
  2505,
  13214,
  43470,
  28581,
  34146,
  38022,
  12339,
  21199,
  12925,
  36660,
  9224,
  239,
  1519,
  15572,
  1124,
  18004,
  33927,
  38107,
  43317,
  7212,
  32120,
  13102,
  8492,
  2373,
  14749,
  10458,
  8231,
  11892,
  2275,
  36955,
  15808,
  22321,
  29009,
  36803,
  41997,
  15710,
  9087,
  12374,
  39415,
  43960,
  25,
  34864,
  37809,
  23299,
  2880,
  15485,
  1910,
  16391,
  44413,
  9599,
  14332,
  25202,
  11003,
  36331,
  30960,
  30418,
  38908,
  29984,
  2841,
  36761,
  27608,
  26456,
  10740,
  40651,
  7610,
  36916,
  34370,
  33389,
  38021,
  6924,
  11845,
  37863,
  16378,
  17996,
  13855,
  391,
  20992,
  41152,
  6446,
  8295,
  41342,
  15564,
  10835,
  15691,
  6365,
  10215,
  43312,
  10157,
  27160,
  1637,
  6652,
  15595,
  6662,
  16152,
  7161,
  29090,
  43061,
  26518,
  38600,
  26489,
  15457,
  10906,
  34866,
  33044,
  42098,
  8495,
  18686,
  40986,
  36129,
  13241,
  2928,
  6945,
  28909,
  8869,
  34973,
  15976,
  42589,
  41110,
  11665,
  13705,
  13436,
  37450,
  4499,
  9368,
  12730,
  21897,
  12085,
  42736,
  22683,
  11564,
  10274,
  14805,
  9961,
  39388,
  6946,
  11082,
  42230,
  8936,
  10895,
  42833,
  5185,
  27948,
  34820,
  29130,
  3579,
  15566,
  44465,
  16431,
  16091,
  29008,
  43313,
  8501,
  19543,
  12923,
  19399,
  16133,
  6421,
  35801,
  2220,
  7139,
  39897,
  1169,
  4163,
  8883,
  26425,
  5102,
  7352,
  11629,
  12790,
  20929,
  14702,
  18011,
  38248,
  9729,
  9654,
  2596,
  36184,
  16395,
  26495,
  25144,
  35134,
  21983,
  2603,
  26177,
  16682,
  6429,
  647,
  1132,
  4822,
  15800,
  23932,
  79,
  12398,
  16404,
  44257,
  8010,
  14673,
  32387,
  10891,
  42392,
  32595,
  5240,
  32396,
  61,
  37095,
  42,
  39662,
  20376,
  43908,
  25178,
  34725,
  5512,
  5552,
  10015,
  41329,
  13836,
  10446,
  9840,
  6441,
  42693,
  15613,
  20166,
  24948,
  35556,
  1131,
  43416,
  30014,
  4103,
  13498,
  14159,
  33738,
  10000,
  33437,
  36431,
  40956,
  16712,
  13311,
  1302,
  38071,
  13105,
  32121,
  32400,
  23143,
  43554,
  42103,
  21706,
  2511,
  564,
  24425,
  16714,
  15302,
  25158,
  43345,
  2783,
  36478,
  28853,
  23066,
  14130,
  2513,
  40964,
  15706,
  35109,
  39302,
  14657,
  41100,
  5165,
  30524,
  35895,
  34948,
  21706,
  25923,
  5637,
  11557,
  12071,
  40127,
  22042,
  26481,
  36390,
  6017,
  9938,
  9865,
  6419,
  11617,
  43348,
  8726,
  19272,
  7813,
  42839,
  21933,
  12071,
  21009,
  16308,
  9703,
  36432,
  20806,
  15266,
  1395,
  7939,
  33462,
  5070,
  37122,
  14859,
  8735,
  3019,
  14087,
  10379,
  199,
  6766,
  37541,
  9553,
  15994,
  3602,
  7835,
  10392,
  9665,
  21710,
  6004,
  35050,
  2879,
  15727,
  44385
]

const filterAnimeData = async () => {
  try {
    // Read the annSongId values from the extracted file
    const idsData = await readFile('ann_song_ids.json', 'utf8');
    const targetAnnSongIds = JSON.parse(idsData);

    // Read the input anime data file
    const data = await readFile('anime_data.json', 'utf8');

    // Parse the JSON data
    const animeData = JSON.parse(data);

    // Filter entries based on annSongId
    const filteredData = animeData.filter(entry =>
      targetAnnSongIds.includes(entry.annSongId)
    );

    // Write the filtered data to a new file
    await writeFile('filtered_anime_data.json', JSON.stringify(filteredData, null, 2));

    console.log(`Filtering complete. Found ${filteredData.length} entries with the specified annSongIds.`);
  } catch (error) {
    console.error('Error:', error.message);
  }
};

filterAnimeData();