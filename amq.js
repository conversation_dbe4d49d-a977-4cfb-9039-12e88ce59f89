import { promises as fs } from 'fs';
import { parse } from 'csv-parse';
import { stringify } from 'csv-stringify/sync';

const readJSON = async (filePath) => {
    try {
        const data = await fs.readFile(filePath, 'utf8');
        return JSON.parse(data);
    } catch (error) {
        console.error(`Error reading JSON file: ${error}`);
        throw error;
    }
};

const readCSV = async (filePath) => {
    try {
        const content = await fs.readFile(filePath, 'utf8');
        return new Promise((resolve, reject) => {
            parse(content, {
                columns: true,
                skip_empty_lines: true,
                trim: true
            }, (err, data) => {
                if (err) reject(err);
                else resolve(data);
            });
        });
    } catch (error) {
        console.error(`Error reading CSV file: ${error}`);
        throw error;
    }
};

const cleanString = (str) => {
    return str
        .toLowerCase()
        // Remove diacritics/accents
        .normalize('NFD')
        .replace(/[\u0300-\u036f]/g, '')
        // Remove special characters and extra whitespace
        .replace(/[^a-z0-9]/g, '')
        .trim();
};

const findMatchingSong = (row, amqData) => {
    const { 'tytuł piosenki': songName, anime: animeTitle, typ: type } = row;

    // Clean the input type first
    const cleanType = cleanString(type);

    // Match song type using object literal instead of switch
    const typeMatchers = {
        'ed': songType => cleanString(songType).startsWith('ending'),
        'op': songType => cleanString(songType).startsWith('opening'),
        'in': songType => cleanString(songType) === 'insertsong'
    };

    return amqData.find(song => {
        // Match song name (clean and compare)
        if (cleanString(song.songName) !== cleanString(songName)) return false;

        // Match anime name (Japanese, English, or Alt names, cleaned)
        const cleanAnimeTitle = cleanString(animeTitle);

        // Check main names
        if (cleanString(song.animeJPName) === cleanAnimeTitle ||
            cleanString(song.animeENName) === cleanAnimeTitle) {
            return typeMatchers[cleanType]?.(song.songType) ?? false;
        }

        // Check alternate names if they exist
        if (song.animeAltName) {
            // Handle both string and array cases
            const altNames = Array.isArray(song.animeAltName)
                ? song.animeAltName
                : [song.animeAltName];

            // If any alternate name matches, consider it a match
            if (altNames.some(altName => cleanString(altName) === cleanAnimeTitle)) {
                return typeMatchers[cleanType]?.(song.songType) ?? false;
            }
        }

        return false;
    });
};

const processFiles = async () => {
    try {
        // Read input files
        const [amqData, csvData, outputTemplate] = await Promise.all([
            readJSON('amqlist.json'),
            readCSV('input.csv'),
            readJSON('output.json')
        ]);

        // Arrays to store processed songs
        const unmatchedSongs = [];

        // Process each row from CSV
        for (const row of csvData) {
            const matchedSong = findMatchingSong(row, amqData);

            if (matchedSong) {
                // Add matched song to output template
                outputTemplate.ruleBlocks[0].blocks.push({
                    annSongId: matchedSong.annSongId
                });
            } else {
                // Add unmatched song to array using object property shorthand
                const { 'tytuł piosenki': songName, anime: animeTitle, typ: type, kto: addedBy } = row;
                unmatchedSongs.push({
                    songName,
                    anime: animeTitle,
                    type,
                    addedBy
                });
            }
        }

        // Write output files using Promise.all for parallel execution
        await Promise.all([
            fs.writeFile('output.json', JSON.stringify(outputTemplate, null, 4)),
            unmatchedSongs.length > 0 ?
                fs.writeFile('unmatched_songs.csv',
                    stringify(unmatchedSongs, {
                        header: true,
                        columns: ['songName', 'anime', 'type', 'addedBy']
                    })
                ) : Promise.resolve()
        ]);

        // Log results using template literals
        console.log('Processing complete!');
        console.log(`Found matches: ${outputTemplate.ruleBlocks[0].blocks.length}`); // -2 for initial template entries
        console.log(`Unmatched songs: ${unmatchedSongs.length}`);

    } catch (error) {
        console.error('Error processing files:', error);
    }
};

// Run the script using top-level await (requires Node.js 14.8+)
await processFiles();