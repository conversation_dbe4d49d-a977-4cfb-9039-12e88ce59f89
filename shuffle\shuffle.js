import { readFileSync, writeFileSync } from 'fs';
import { randomBytes } from 'crypto';

// Function to perform Fisher-Yates shuffle with crypto
const shuffleArray = array => {
  for (let i = array.length - 1; i > 0; i--) {
    const j = Math.floor(Number(randomBytes(4).readUInt32LE()) / 0x100000000 * (i + 1));
    [array[i], array[j]] = [array[j], array[i]];
  }
  return array;
};

try {
  // Read and parse the JSON file
  const data = JSON.parse(readFileSync('shuffle/songs.json', 'utf8'));

  // Shuffle each ruleBlock's blocks array
  data.ruleBlocks.forEach(ruleBlock => {
    if (ruleBlock.blocks) {
      ruleBlock.blocks = shuffleArray(ruleBlock.blocks);
    }
  });

  // Write the shuffled JSON back to a file
  writeFileSync('shuffle/shuffled_output.json', JSON.stringify(data, null, 4));
  console.log('Successfully shuffled and saved the JSON file!');

} catch (error) {
  console.error('Error:', error.message);
}